// Simple test script to verify API connectivity
// Using built-in fetch (Node.js 18+)

async function testAPI() {
  console.log('Testing API connectivity...');
  
  try {
    // Test health endpoint
    console.log('1. Testing health endpoint...');
    const healthResponse = await fetch('http://localhost:3000/health');
    const healthData = await healthResponse.json();
    console.log('✅ Health endpoint working:', healthData.data.status);
    
    // Test tasks endpoint with dev headers
    console.log('2. Testing tasks endpoint with dev headers...');
    const tasksResponse = await fetch('http://localhost:3000/tasks', {
      headers: {
        'X-Dev-Mode': 'true',
        'X-Dev-User-Id': '5',
        'Content-Type': 'application/json'
      }
    });
    
    if (tasksResponse.ok) {
      const tasksData = await tasksResponse.json();
      console.log('✅ Tasks endpoint working:', tasksData);
    } else {
      console.log('❌ Tasks endpoint failed:', tasksResponse.status, tasksResponse.statusText);
      const errorText = await tasksResponse.text();
      console.log('Error details:', errorText);
    }
    
    // Test finances endpoint
    console.log('3. Testing finances endpoint...');
    const financesResponse = await fetch('http://localhost:3000/finances', {
      headers: {
        'X-Dev-Mode': 'true',
        'X-Dev-User-Id': '5',
        'Content-Type': 'application/json'
      }
    });
    
    if (financesResponse.ok) {
      const financesData = await financesResponse.json();
      console.log('✅ Finances endpoint working:', financesData);
    } else {
      console.log('❌ Finances endpoint failed:', financesResponse.status, financesResponse.statusText);
    }
    
    // Test ideas endpoint
    console.log('4. Testing ideas endpoint...');
    const ideasResponse = await fetch('http://localhost:3000/ideas', {
      headers: {
        'X-Dev-Mode': 'true',
        'X-Dev-User-Id': '5',
        'Content-Type': 'application/json'
      }
    });
    
    if (ideasResponse.ok) {
      const ideasData = await ideasResponse.json();
      console.log('✅ Ideas endpoint working:', ideasData);
    } else {
      console.log('❌ Ideas endpoint failed:', ideasResponse.status, ideasResponse.statusText);
    }
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

testAPI();
