// Simple test script to verify database connectivity and data
const mysql = require('mysql2/promise');

async function testDatabase() {
  console.log('Testing database connectivity...');
  
  try {
    // Create connection
    const connection = await mysql.createConnection({
      host: '*************',
      port: 3306,
      user: 'dupli',
      password: '9fya_%LkQW6dF45',
      database: 'dupli'
    });
    
    console.log('✅ Database connection established');
    
    // Test 1: Check if user ID 5 exists
    console.log('1. Checking if user ID 5 exists...');
    const [users] = await connection.execute('SELECT id, name, email FROM users WHERE id = ?', [5]);
    if (users.length > 0) {
      console.log('✅ User ID 5 exists:', users[0]);
    } else {
      console.log('❌ User ID 5 does not exist');
      
      // Check what users exist
      const [allUsers] = await connection.execute('SELECT id, name, email FROM users LIMIT 5');
      console.log('Available users:', allUsers);
    }
    
    // Test 2: Check tasks table
    console.log('2. Checking tasks table...');
    const [tasks] = await connection.execute('SELECT COUNT(*) as count FROM tasks WHERE user_id = ?', [5]);
    console.log('Tasks for user ID 5:', tasks[0].count);
    
    // Test 3: Check finances table
    console.log('3. Checking finances table...');
    const [finances] = await connection.execute('SELECT COUNT(*) as count FROM finances WHERE user_id = ?', [5]);
    console.log('Finances for user ID 5:', finances[0].count);
    
    // Test 4: Check ideas table
    console.log('4. Checking ideas table...');
    const [ideas] = await connection.execute('SELECT COUNT(*) as count FROM ideas WHERE user_id = ?', [5]);
    console.log('Ideas for user ID 5:', ideas[0].count);
    
    // Test 5: Check table structures
    console.log('5. Checking table structures...');
    const [taskColumns] = await connection.execute('DESCRIBE tasks');
    console.log('Tasks table columns:', taskColumns.map(col => col.Field));
    
    await connection.end();
    console.log('✅ Database tests completed');
    
  } catch (error) {
    console.error('❌ Database test failed:', error.message);
  }
}

testDatabase();
